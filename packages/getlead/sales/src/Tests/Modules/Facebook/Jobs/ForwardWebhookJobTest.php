<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Jobs;

use App\Modules\Facebook\Jobs\ForwardWebhookJob;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

final class ForwardWebhookJobTest extends TestCase
{
    /**
     * @test
     */
    public function it_should_forward_webhook_successfully(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        $webhookData = [
            'object' => 'page',
            'entry' => [
                [
                    'id' => '123456789',
                    'time' => 1234567890,
                    'changes' => [
                        [
                            'value' => [
                                'page_id' => '123456789',
                                'form_id' => '987654321',
                                'leadgen_id' => '555666777',
                                'created_time' => '1234567890'
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $headers = ['X-Custom-Header' => 'test-value'];

        Http::fake([
            $forwardUrl => Http::response(['status' => 'success'], 200)
        ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Processing webhook forwarding job', \Mockery::type('array'));

        Log::shouldReceive('info')
            ->once()
            ->with('Successfully forwarded Facebook webhook via job', \Mockery::type('array'));

        $job = new ForwardWebhookJob($forwardUrl, $webhookData, $headers);
        $job->handle();

        Http::assertSent(function ($request) use ($forwardUrl, $webhookData, $headers) {
            return $request->url() === $forwardUrl
                && $request->data() === $webhookData
                && $request->hasHeader('Content-Type', 'application/json')
                && $request->hasHeader('User-Agent', 'CRM-Facebook-Webhook-Forwarder/1.0')
                && $request->hasHeader('X-Custom-Header', 'test-value');
        });
    }

    /**
     * @test
     */
    public function it_should_handle_http_failure(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        $webhookData = ['test' => 'data'];

        Http::fake([
            $forwardUrl => Http::response(['error' => 'Server error'], 500)
        ]);

        Log::shouldReceive('info')
            ->once()
            ->with('Processing webhook forwarding job', \Mockery::type('array'));

        Log::shouldReceive('warning')
            ->once()
            ->with('Failed to forward Facebook webhook via job', \Mockery::type('array'));

        $job = new ForwardWebhookJob($forwardUrl, $webhookData);
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('HTTP request failed with status: 500');
        
        $job->handle();
    }

    /**
     * @test
     */
    public function it_should_handle_exception(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        $webhookData = ['test' => 'data'];

        Http::fake(function () {
            throw new \Exception('Network error');
        });

        Log::shouldReceive('info')
            ->once()
            ->with('Processing webhook forwarding job', \Mockery::type('array'));

        Log::shouldReceive('error')
            ->once()
            ->with('Exception occurred while forwarding Facebook webhook via job', \Mockery::type('array'));

        $job = new ForwardWebhookJob($forwardUrl, $webhookData);
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Network error');
        
        $job->handle();
    }

    /**
     * @test
     */
    public function it_should_log_when_job_fails_permanently(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        $webhookData = ['test' => 'data'];
        $exception = new \Exception('Permanent failure');

        Log::shouldReceive('error')
            ->once()
            ->with('Webhook forwarding job failed permanently', [
                'forward_url' => $forwardUrl,
                'exception' => 'Permanent failure',
            ]);

        $job = new ForwardWebhookJob($forwardUrl, $webhookData);
        $job->failed($exception);
    }
}
