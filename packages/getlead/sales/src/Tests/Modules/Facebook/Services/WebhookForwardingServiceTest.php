<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Services;

use App\Modules\Facebook\Services\WebhookForwardingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

final class WebhookForwardingServiceTest extends TestCase
{
    private WebhookForwardingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new WebhookForwardingService();
    }

    /**
     * @test
     */
    public function it_should_skip_forwarding_when_url_not_configured(): void
    {
        Config::set('services.facebook.webhook_forward_url', '');
        
        Log::shouldReceive('info')
            ->once()
            ->with('Facebook webhook forward URL not configured, skipping forwarding');

        $request = Request::create('/webhook', 'POST', ['test' => 'data']);
        
        $this->service->forwardWebhook($request);
    }

    /**
     * @test
     */
    public function it_should_forward_webhook_successfully(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        Config::set('services.facebook.webhook_forward_url', $forwardUrl);
        Config::set('app.url', 'https://app.getlead.co.uk');

        Http::fake([
            $forwardUrl => Http::response(['status' => 'success'], 200)
        ]);

        $requestData = [
            'object' => 'page',
            'entry' => [
                [
                    'id' => '123456789',
                    'time' => 1234567890,
                    'changes' => [
                        [
                            'value' => [
                                'page_id' => '123456789',
                                'form_id' => '987654321',
                                'leadgen_id' => '555666777',
                                'created_time' => '1234567890'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $request = Request::create('/webhook', 'POST', $requestData);
        $request->headers->set('HOST', 'app.getlead.co.uk');

        Log::shouldReceive('info')
            ->once()
            ->with('Forwarding Facebook webhook to external domain', \Mockery::type('array'));

        Log::shouldReceive('info')
            ->once()
            ->with('Successfully forwarded Facebook webhook', \Mockery::type('array'));

        $this->service->forwardWebhook($request);

        Http::assertSent(function ($request) use ($forwardUrl, $requestData) {
            return $request->url() === $forwardUrl
                && $request->data() === $requestData
                && $request->hasHeader('Content-Type', 'application/json')
                && $request->hasHeader('User-Agent', 'CRM-Facebook-Webhook-Forwarder/1.0')
                && $request->hasHeader('X-Forwarded-From', 'https://app.getlead.co.uk')
                && $request->hasHeader('X-Original-Host', 'app.getlead.co.uk');
        });
    }

    /**
     * @test
     */
    public function it_should_handle_forwarding_failure(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        Config::set('services.facebook.webhook_forward_url', $forwardUrl);

        Http::fake([
            $forwardUrl => Http::response(['error' => 'Server error'], 500)
        ]);

        $request = Request::create('/webhook', 'POST', ['test' => 'data']);

        Log::shouldReceive('info')
            ->once()
            ->with('Forwarding Facebook webhook to external domain', \Mockery::type('array'));

        Log::shouldReceive('warning')
            ->once()
            ->with('Failed to forward Facebook webhook', \Mockery::type('array'));

        $this->service->forwardWebhook($request);
    }

    /**
     * @test
     */
    public function it_should_handle_forwarding_exception(): void
    {
        $forwardUrl = 'https://external-domain.com/webhook';
        Config::set('services.facebook.webhook_forward_url', $forwardUrl);

        Http::fake(function () {
            throw new \Exception('Network error');
        });

        $request = Request::create('/webhook', 'POST', ['test' => 'data']);

        Log::shouldReceive('info')
            ->once()
            ->with('Forwarding Facebook webhook to external domain', \Mockery::type('array'));

        Log::shouldReceive('error')
            ->once()
            ->with('Exception occurred while forwarding Facebook webhook', \Mockery::type('array'));

        $this->service->forwardWebhook($request);
    }
}
