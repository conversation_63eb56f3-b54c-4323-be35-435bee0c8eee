<?php

namespace App\FrontendModel;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;

class DealAdditionalField extends Model
{
    const TYPES = [
        1 => 'Text',
        2 => 'Dropdown',
        3 => 'Date',
        4 => 'Time',
        5 => 'DateTime',
        6 => 'Image',
        7 => 'Number',
        8 => 'Multi Select Dropdown'
    ];
    protected $appends = ['type_text', 'decoded_values'];

    public static function addFldCount()
    {
        return $additional_fields = LeadAdditionalField::where('vendor_id', User::getVendorId())->count();

    }

    public function getTypeTextAttribute()
    {
        return isset(self::TYPES[$this->input_type]) ? self::TYPES[$this->input_type] : "NA";
    }

    public function getDecodedValuesAttribute()
    {
        $isApiRequest = Request::is('api/*');
        if ($isApiRequest)
            return null;

        try {
            if (is_array($this->values))
                return $this->values;
            else
                return $this->values ? json_decode($this->values, true) : null;
        } catch (\Exception $exp) {
            return null;
        }
    }

    public function additionalDetails()
    {
        return $this->hasMany('App\FrontendModel\DealAdditionalDetails', 'field_id', 'id');
    }
}
