<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Modules\Facebook\Services\WebhookForwardingService;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

class TestFacebookWebhookForwarding extends Command
{
    protected $signature = 'facebook:test-webhook-forwarding 
                            {url : The URL to forward the test webhook to}
                            {--async : Forward the webhook asynchronously}';

    protected $description = 'Test Facebook webhook forwarding functionality';

    public function handle(WebhookForwardingService $forwardingService): int
    {
        $url = $this->argument('url');
        $async = $this->option('async');

        // Temporarily set the forward URL
        Config::set('services.facebook.webhook_forward_url', $url);
        Config::set('services.facebook.webhook_forward_async', $async);

        $this->info('Testing Facebook webhook forwarding...');
        $this->info("Forward URL: {$url}");
        $this->info("Async mode: " . ($async ? 'enabled' : 'disabled'));

        // Create a sample webhook request
        $sampleData = [
            'object' => 'page',
            'entry' => [
                [
                    'id' => '123456789',
                    'time' => time(),
                    'changes' => [
                        [
                            'value' => [
                                'page_id' => '123456789',
                                'form_id' => '987654321',
                                'leadgen_id' => '555666777',
                                'created_time' => (string) time()
                            ],
                            'field' => 'leadgen'
                        ]
                    ]
                ]
            ]
        ];

        // Create a mock request
        $request = Request::create('/test-webhook', 'POST', $sampleData);
        $request->headers->set('HOST', 'test.example.com');

        try {
            $forwardingService->forwardWebhook($request, $async);
            
            if ($async) {
                $this->info('✅ Webhook forwarding job dispatched successfully!');
                $this->info('Check your queue workers and logs to see the forwarding result.');
            } else {
                $this->info('✅ Webhook forwarded successfully!');
            }
            
            $this->info('Check the destination URL and application logs for details.');
            
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Failed to forward webhook: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
