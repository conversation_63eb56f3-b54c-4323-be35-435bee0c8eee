<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Services;

use App\Modules\Facebook\Jobs\ForwardWebhookJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class WebhookForwardingService
{
    private const HTTP_TIMEOUT = 30;
    private const HTTP_RETRY_ATTEMPTS = 3;
    private const HTTP_RETRY_DELAY = 1000; // milliseconds

    public function forwardWebhook(Request $request, bool $async = false): void
    {
        $forwardUrl = config('services.facebook.webhook_forward_url');

        if (empty($forwardUrl)) {
            Log::info('Facebook webhook forward URL not configured, skipping forwarding');
            return;
        }

        $headers = [
            'X-Forwarded-From' => config('app.url'),
            'X-Original-Host' => $request->getHost(),
        ];

        if ($async) {
            Log::info('Dispatching Facebook webhook forwarding job', [
                'forward_url' => $forwardUrl,
            ]);

            ForwardWebhookJob::dispatch($forwardUrl, $request->all(), $headers);
            return;
        }

        Log::info('Forwarding Facebook webhook to external domain', [
            'forward_url' => $forwardUrl,
            'original_data' => $request->all(),
        ]);

        try {
            $response = Http::timeout(self::HTTP_TIMEOUT)
                ->retry(self::HTTP_RETRY_ATTEMPTS, self::HTTP_RETRY_DELAY)
                ->withHeaders(array_merge([
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'CRM-Facebook-Webhook-Forwarder/1.0',
                ], $headers))
                ->post($forwardUrl, $request->all());

            if ($response->successful()) {
                Log::info('Successfully forwarded Facebook webhook', [
                    'forward_url' => $forwardUrl,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                ]);
            } else {
                Log::warning('Failed to forward Facebook webhook', [
                    'forward_url' => $forwardUrl,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while forwarding Facebook webhook', [
                'forward_url' => $forwardUrl,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
