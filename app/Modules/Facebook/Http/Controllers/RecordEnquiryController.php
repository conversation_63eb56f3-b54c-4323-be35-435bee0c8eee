<?php

namespace App\Modules\Facebook\Http\Controllers;

use App\Modules\Facebook\Jobs\RecordEnquiry\ProcessRecordEnquiryRequest;
use App\Modules\Facebook\Models\FbWorkFlow;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Models\RecordEnquiryRequestStatus;
use App\Modules\Facebook\Services\WebhookForwardingService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

final class RecordEnquiryController
{
    public function __construct(
        private readonly WebhookForwardingService $webhookForwardingService
    ) {
    }

    public function __invoke(Request $request): Response
    {
        Log::info('Record enquiry request', [
            'request' => $request->all(),
        ]);

        // Forward the webhook to external domain if configured
        $this->webhookForwardingService->forwardWebhook(
            $request,
            config('services.facebook.webhook_forward_async', false)
        );

        if($this->isPermissionChangeWebhook($request)) {
            return response('Webhook ignored', Response::HTTP_ACCEPTED);
        }

        $data = $this->parseRequest(
            enquiryRequest: $request->input('entry.0.changes.0.value')
        );

        Log::info('Record enquiry parsed data', [
            'data' => $data,
        ]);

        $vendorId = $this->getVendorId(pageId: $data['page_id']);

        if (!$vendorId) {
            Log::info('Vendor id not found', [
                'page_id' => $data['page_id'],
            ]);

            return response('Webhook handled', Response::HTTP_ACCEPTED);
        }

        Log::info('Vendor id found', [
            'vendor_id' => $vendorId,
        ]);

        $recordEnquiryRequest = RecordEnquiryRequest::create([
            'page_id' => $data['page_id'],
            'form_id' => $data['form_id'],
            'lead_gen_id' => $data['leadgen_id'],
            'vendor_id' => $vendorId,
            'received_at' => $data['created_time'],
            'status' => RecordEnquiryRequestStatus::Pending,
        ]);

        Bus::dispatch(new ProcessRecordEnquiryRequest(
            recordEnquiryId: $recordEnquiryRequest->id
        ));

        return response('Webhook handled', Response::HTTP_ACCEPTED);
    }

    /**
     * @return array{
     *     page_id: int,
     *     form_id: int,
     *     leadgen_id: int,
     *     created_time: Carbon,
     * }
     */
    private function parseRequest(array $enquiryRequest): array
    {
        return [
            'page_id' => (int) $enquiryRequest['page_id'],
            'form_id' => (int) $enquiryRequest['form_id'],
            'leadgen_id' => (int) $enquiryRequest['leadgen_id'],
            'created_time' => Carbon::createFromTimestamp((int) $enquiryRequest['created_time']),
        ];
    }

    private function getVendorId(int $pageId): ?int
    {
        /** @var int|null */
        return FbWorkFlow::query()
            ->where('fb_page_id', '=', $pageId)
            ->value('vendor_id');
    }

    private function isPermissionChangeWebhook(Request $request): bool
    {
        return (string) $request->input('object') === 'permissions';
    }
}
