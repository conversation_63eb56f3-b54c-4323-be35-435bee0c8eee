<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LogWebhookForwarding
{
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        
        Log::info('Facebook webhook received', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
            'payload_size' => strlen(json_encode($request->all())),
        ]);

        $response = $next($request);

        $endTime = microtime(true);
        $processingTime = round(($endTime - $startTime) * 1000, 2); // milliseconds

        Log::info('Facebook webhook processed', [
            'status_code' => $response->getStatusCode(),
            'processing_time_ms' => $processingTime,
            'response_size' => strlen($response->getContent()),
        ]);

        return $response;
    }
}
