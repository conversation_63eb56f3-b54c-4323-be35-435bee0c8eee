<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class ForwardWebhookJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $queue = 'integration:meta';

    private const HTTP_TIMEOUT = 30;
    private const HTTP_RETRY_ATTEMPTS = 3;
    private const HTTP_RETRY_DELAY = 1000; // milliseconds

    public function __construct(
        private readonly string $forwardUrl,
        private readonly array $webhookData,
        private readonly array $headers = []
    ) {
    }

    public function handle(): void
    {
        Log::info('Processing webhook forwarding job', [
            'forward_url' => $this->forwardUrl,
            'data_size' => count($this->webhookData),
        ]);

        try {
            $response = Http::timeout(self::HTTP_TIMEOUT)
                ->retry(self::HTTP_RETRY_ATTEMPTS, self::HTTP_RETRY_DELAY)
                ->withHeaders(array_merge([
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'CRM-Facebook-Webhook-Forwarder/1.0',
                ], $this->headers))
                ->post($this->forwardUrl, $this->webhookData);

            if ($response->successful()) {
                Log::info('Successfully forwarded Facebook webhook via job', [
                    'forward_url' => $this->forwardUrl,
                    'status_code' => $response->status(),
                ]);
            } else {
                Log::warning('Failed to forward Facebook webhook via job', [
                    'forward_url' => $this->forwardUrl,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                ]);
                
                // Retry the job if it failed
                $this->fail('HTTP request failed with status: ' . $response->status());
            }
        } catch (\Exception $e) {
            Log::error('Exception occurred while forwarding Facebook webhook via job', [
                'forward_url' => $this->forwardUrl,
                'exception' => $e->getMessage(),
            ]);
            
            // Retry the job if an exception occurred
            $this->fail($e);
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Webhook forwarding job failed permanently', [
            'forward_url' => $this->forwardUrl,
            'exception' => $exception->getMessage(),
        ]);
    }
}
